<div class="dropdown">
    <div class="d-flex d-inline-flex align-items-baseline">
    <!-- Upload Icon -->
    <mat-icon class="material-symbols-outlined menu-icon fs-6 text-end">ios_share</mat-icon>
    <!-- Trigger <PERSON>ton -->
    <button class="btn btn-link fs-6 fw-semibold" style="color: #666666 !important;"
            type="button"
            (click)="toggleDropdown()"
            [attr.aria-expanded]="isOpen">
        Download or Print
    </button>
    </div>

    <!-- Dropdown menu -->
    <div class="dropdown-menu custom-menu shadow-lg p-0"
         [class.show]="isOpen"
         *ngIf="isOpen">
        <!-- Menu Header -->
        <div class="d-flex align-items-center justify-content-between px-3 py-2 border-bottom">
            <div class="d-flex align-items-center gap-2">
                <!-- Header Icon -->
                <mat-icon class="fs-6 material-symbols-outlined menu-icon">ios_share</mat-icon>
                <h3 class="fs-6 fw-lighter mb-0 text-dark fst-italic">Export Report</h3>
            </div>
            <!-- Close Button -->
            <button type="button" class="btn-close" style="font-size: 0.5rem;" (click)="closeDropdown()" aria-label="Close"></button>
        </div>

        <!-- Menu Items -->
        <ul class="list-unstyled mb-0 py-1">
             <li><a class="dropdown-item fs-6 fw-bold" href="#" (click)="onPrintClick(); $event.preventDefault();">Print</a></li>
             <li><hr class="dropdown-divider mx-2"></li>
             <li><a class="dropdown-item fs-6 fw-bold" href="#" (click)="onPdfExportClick(); $event.preventDefault();">PDF Export</a></li>
             <li><a class="dropdown-item fs-6 fw-bold" href="#" (click)="onExcelExportClick(); $event.preventDefault();">Excel Export</a></li>
             <li><a class="dropdown-item fs-6 fw-bold" href="#" (click)="onCsvExportClick(); $event.preventDefault();">CSV Export</a></li>
        </ul>
    </div>
</div>
