<div class="dropdown">
    <!-- Trigger <PERSON> -->
    <button class="btn btn-light border shadow-sm d-inline-flex align-items-center fs-5 fw-semibold" type="button" id="dropdownMenuButton" data-bs-toggle="dropdown" aria-expanded="false">
        <!-- Upload Icon -->
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="me-2 text-secondary">
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
            <polyline points="17 8 12 3 7 8" />
            <line x1="12" x2="12" y1="3" y2="15" />
        </svg>
        Download or Print
    </button>

    <!-- Bootstrap Dropdown menu -->
    <div class="dropdown-menu custom-menu shadow-lg p-0" aria-labelledby="dropdownMenuButton">
        <!-- Menu <PERSON>er -->
        <div class="d-flex align-items-center justify-content-between px-3 py-2 border-bottom">
            <div class="d-flex align-items-center gap-2">
                <!-- Header Icon -->
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-secondary">
                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
                    <polyline points="17 8 12 3 7 8" />
                    <line x1="12" x2="12" y1="3" y2="15" />
                </svg>
                <h3 class="fs-6 fw-semibold mb-0 text-dark">Export Report</h3>
            </div>
            <!-- Close Button (data-bs-dismiss="dropdown" closes the menu) -->
            <button type="button" class="btn-close" data-bs-dismiss="dropdown" aria-label="Close"></button>
        </div>

        <!-- Menu Items -->
        <ul class="list-unstyled mb-0 py-1">
             <li><a class="dropdown-item fs-5" href="#" (click)="onPrintClick(); $event.preventDefault();">Print</a></li>
             <li><hr class="dropdown-divider mx-2"></li>
             <li><a class="dropdown-item fs-5" href="#" (click)="onPdfExportClick(); $event.preventDefault();">PDF Export</a></li>
             <li><a class="dropdown-item fs-5" href="#" (click)="onExcelExportClick(); $event.preventDefault();">Excel Export</a></li>
             <li><a class="dropdown-item fs-5" href="#" (click)="onCsvExportClick(); $event.preventDefault();">CSV Export</a></li>
        </ul>
    </div>
</div>
