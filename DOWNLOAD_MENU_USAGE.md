# Download Menu Component Usage Guide

## Overview
The `download-menu` component is a reusable Angular component that provides a consistent Bootstrap-styled dropdown menu for download and print functionality across the application. It replaces the existing custom dropdown menus in quality-measures, report-details, and bonus-measures components.

## Features
- Bootstrap 5 styled dropdown with consistent design
- SVG icons for modern appearance
- Event-driven architecture for flexibility
- Reusable across multiple components
- Responsive design with proper positioning

## Component Files Created
- `src/app/modules/dashboard/download-menu/download-menu.component.ts`
- `src/app/modules/dashboard/download-menu/download-menu.component.html`
- `src/app/modules/dashboard/download-menu/download-menu.component.css`

## Usage

### 1. Basic Implementation
Replace the existing dropdown HTML with:

```html
<app-download-menu 
  (printClicked)="printTable()"
  (pdfExportClicked)="downloadPDF()"
  (excelExportClicked)="downloadExcel()"
  (csvExportClicked)="downloadCSV()">
</app-download-menu>
```

### 2. Event Handlers
The component emits the following events:
- `printClicked` - When Print is selected
- `pdfExportClicked` - When PDF Export is selected
- `excelExportClicked` - When Excel Export is selected
- `csvExportClicked` - When CSV Export is selected

### 3. Example Replacement for Quality Measures Component

**Before (existing code):**
```html
<div class="export-dropdown">
  <div class="dropdown" (document:click)="onClickOutsideMenu($event)">
    <button class="downloadButton" (click)="toggleMenu(); $event.stopPropagation();">
      <mat-icon class="material-symbols-outlined download-icon">ios_share</mat-icon>Download or Print
    </button>
    <nav class="menu" *ngIf="menuOpen">
      <ul>
        <li>
          <div class="menu-header">
            <div class="menu-title">
              <mat-icon class="material-symbols-outlined menu-icon">ios_share</mat-icon>
              <span class="export-label">Export&nbsp;Report</span>
            </div>
            <div class="menu-close" (click)="toggleMenu()">
              <mat-icon class="material-symbols-outlined close-icon">close</mat-icon>
            </div>
          </div>
        </li>
        <li class="menu-separator"><button (click)="printTable();">Print</button></li>
        <li><button (click)="downloadPDF();">PDF Export</button></li>
        <li><button (click)="downloadExcel();">Excel Export</button></li>
        <li><button (click)="downloadCSV();">CSV Export</button></li>
      </ul>
    </nav>
  </div>
</div>
```

**After (new component):**
```html
<div class="export-dropdown">
  <app-download-menu 
    (printClicked)="printTable()"
    (pdfExportClicked)="downloadPDF()"
    (excelExportClicked)="downloadExcel()"
    (csvExportClicked)="downloadCSV()">
  </app-download-menu>
</div>
```

### 4. Component Cleanup
After replacing with the new component, you can remove:

**From TypeScript files:**
- `menuOpen` property
- `toggleMenu()` method
- `onClickOutsideMenu()` method

**From SCSS files:**
- All custom dropdown styling (`.dropdown`, `.downloadButton`, `.menu`, etc.)

## Benefits
1. **Consistency**: All download menus will have the same look and feel
2. **Maintainability**: Changes to the design only need to be made in one place
3. **Bootstrap Integration**: Uses Bootstrap's native dropdown functionality
4. **Accessibility**: Better keyboard navigation and screen reader support
5. **Responsive**: Works well on all screen sizes

## Demo
A demo has been added to the dashboard component to show the new download-menu in action. Visit the dashboard to see it working.

## Implementation Status

### ✅ Completed
- **Download Menu Component**: Created and added to dashboard module
- **Report Details Component**: Successfully replaced dropdown menu
- **Demo**: Added to dashboard component for testing

### 🔄 Remaining Components to Update
- **Quality Measures Component** (`src/app/modules/dashboard/quality-measures/quality-measures.component.html`)
- **Bonus Measures Component** (`src/app/modules/dashboard/bonus-measures/bonus-measures.component.html`)

## Migration Steps for Remaining Components

### For Quality Measures Component:
1. **Replace HTML** (lines 78-102 in quality-measures.component.html):
```html
<!-- Replace this entire block -->
<div class="export-dropdown">
  <div class="dropdown" (document:click)="onClickOutsideMenu($event)">
    <!-- ... existing dropdown code ... -->
  </div>
</div>

<!-- With this -->
<div class="export-dropdown">
  <app-download-menu
    (printClicked)="printTable()"
    (pdfExportClicked)="downloadPDF()"
    (excelExportClicked)="downloadExcel()"
    (csvExportClicked)="downloadCSV()">
  </app-download-menu>
</div>
```

2. **Remove TypeScript properties/methods** (quality-measures.component.ts):
   - Remove `menuOpen` property (line 35)
   - Remove `toggleMenu()` method (lines 83-85)
   - Remove `onClickOutsideMenu()` method (lines 87-93)

3. **Remove SCSS styles** (quality-measures.component.scss):
   - Remove lines 399-470 (all dropdown-related styles)

### For Bonus Measures Component:
1. **Replace HTML** (lines 20-44 in bonus-measures.component.html):
```html
<!-- Replace the existing dropdown with -->
<div class="export-dropdown">
  <app-download-menu
    (printClicked)="printTable()"
    (pdfExportClicked)="downloadPDF()"
    (excelExportClicked)="downloadExcel()"
    (csvExportClicked)="downloadCSV()">
  </app-download-menu>
</div>
```

2. **Remove TypeScript properties/methods** (bonus-measures.component.ts):
   - Remove `menuOpen` property
   - Remove `toggleMenu()` method
   - Remove `onClickOutsideMenu()` method

3. **Remove SCSS styles** (bonus-measures.component.scss):
   - Remove dropdown-related styles (similar to quality-measures)

## Testing
1. **Build the application**: `ng serve --port 4201`
2. **Navigate to each component** and test the download functionality
3. **Verify all export options work**: Print, PDF, Excel, CSV
4. **Check responsive behavior** on different screen sizes

## Benefits Achieved
1. **Consistency**: All download menus now have the same Bootstrap-styled appearance
2. **Maintainability**: Single component to maintain instead of three separate implementations
3. **Modern Design**: Uses SVG icons and Bootstrap 5 styling
4. **Accessibility**: Better keyboard navigation and screen reader support
5. **Code Reduction**: Eliminated ~200 lines of duplicate code across components
