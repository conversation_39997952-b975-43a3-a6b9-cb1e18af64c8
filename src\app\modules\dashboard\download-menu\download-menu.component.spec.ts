import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DownloadMenuComponent } from './download-menu.component';

describe('DownloadMenuComponent', () => {
  let component: DownloadMenuComponent;
  let fixture: ComponentFixture<DownloadMenuComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ DownloadMenuComponent ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(DownloadMenuComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should emit printClicked event when onPrintClick is called', () => {
    spyOn(component.printClicked, 'emit');
    component.onPrintClick();
    expect(component.printClicked.emit).toHaveBeenCalled();
  });

  it('should emit pdfExportClicked event when onPdfExportClick is called', () => {
    spyOn(component.pdfExportClicked, 'emit');
    component.onPdfExportClick();
    expect(component.pdfExportClicked.emit).toHaveBeenCalled();
  });

  it('should emit excelExportClicked event when onExcelExportClick is called', () => {
    spyOn(component.excelExportClicked, 'emit');
    component.onExcelExportClick();
    expect(component.excelExportClicked.emit).toHaveBeenCalled();
  });

  it('should emit csvExportClicked event when onCsvExportClick is called', () => {
    spyOn(component.csvExportClicked, 'emit');
    component.onCsvExportClick();
    expect(component.csvExportClicked.emit).toHaveBeenCalled();
  });
});
