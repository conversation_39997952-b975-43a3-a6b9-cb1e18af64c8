.custom-menu {
    width: 250px;
    margin-top: 0.75rem !important; /* Add some space for the arrow */
}

/* The ::before pseudo-element creates the small arrow above the menu.
  Using :host-context allows us to style based on the parent, but here we'll
  just apply it directly. The styles are scoped by Angular.
*/
.custom-menu::before {
    content: '';
    position: absolute;
    top: -8px;
    right: 18px;
    width: 16px;
    height: 16px;
    background-color: white;
    border-top: 1px solid var(--bs-border-color);
    border-left: 1px solid var(--bs-border-color);
    transform: rotate(45deg);
    z-index: -1;
}
