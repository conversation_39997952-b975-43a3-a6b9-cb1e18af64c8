// this file is based on the "modern" theme for Tabulator
// you set the variables on the top and then import the Tabulator scss file
// and then override the variables you want to change
// https://tabulator.info/docs/6.3/theme#build

$primary: #333333 !default; //the base text color from which the rest of the theme derives

//Main Theme Variables
$backgroundColor: #efefef !default; //background color of tabulator
$borderColor: #cdcdcd !default; //border to tabulator
$textSize: 13px !default; //table text size
$lightBlueTextColor: #32a6f4 !default; //light blue text color
$darkBlueTextColor: #0071bc !default; //dark blue text color

//header theming
$headerBackgroundColor: #0071bc !default; //border to tabulator
$headerTextColor:  #ffffff !default; //header text color
$headerBorderColor: $headerBackgroundColor !default;  //header border color
$headerSeparatorColor: #e6e6e6 !default; //header bottom separator color
$headerMargin: 4px !default; //padding round header

//column header arrows
$sortArrowActive: #66a9d7 !default;
$sortArrowInactive: lighten(#66a9d7, 30%) !default;

//group header arrow
$groupArrowColor: #333333 !default; //color of the group arrow

//row theming
$rowBackgroundColor: #ffffff !default; //table row background color
$rowAltBackgroundColor: #ffffff !default; //table row background color
$rowBorderColor: #e4e4e4 !default; //table row border color
$rowTextColor: #363636 !default; //table row text color
$rowHoverBackground: #bbb !default; //row background color on hover -- I have disabled the hover effect on Quality Measures, so keeping this in.

$rowSelectedBackground: #9ABCEA !default; //row background color when selected
$rowSelectedBackgroundHover: #769BCC !default; //row background color when selected and hovered

$editBoxColor: #1D68CD !default; //border color for edit boxes
$errorColor: #dd0000 !default; //error indication

//footer theming - not sure what to put for footers, so copying the "Data Notes" as it is the only footer I have seen so far
$footerBackgroundColor: #f2f2f2 !default; //footer border color
$footerTextColor: #666666 !default; //footer text color
$footerBorderColor: #d2d2d2 !default; //footer border color
$footerSeparatorColor: #999 !default; //footer bottom separator color
$footerActiveColor: $primary !default; //footer bottom active text color

//handle theming - I don't use this, so leaving defaults
$handleWidth: 10px !default; //width of the row handle
$handleColor: $primary !default; //color for odd numbered rows
$handleColorAlt: lighten($primary, 10%) !default; //color for even numbered rows

//range selection
$rangeBorderColor: #{darken($primary, 10%)} !default; //range border color
$rangeHandleColor: $rangeBorderColor !default; //range handle color
$rangeHeaderSelectedBackground: $rangeBorderColor !default; //header background color when selected
$rangeHeaderHighlightBackground: $primary !default; //header background color when highlighted
$rangeHeaderTextHighlightBackground: #fff !default; //header text color when highlighted

//popup theming
$popupBackgroundColor: #f2f2f2 !default; //background color of popup

//group row theming
// function to make a lighter background based on the base color
$base-background-color: #e6e6e6; // You can set your desired base color here

// Function to generate progressively lighter background colors
@function lighten-background($base-color, $level) {
  $amount: $level * 3; // Increase of 3 for each level
  $r: red($base-color) + $amount;
  $g: green($base-color) + $amount;
  $b: blue($base-color) + $amount;

  @return rgb(min($r, 255), min($g, 255), min($b, 255));
}

// Function to adjust border colors based on the group level
@function adjust-border-color($base-color, $level, $type) {
  $adjustment: 0; // Default adjustment
  $amount: $level * 3; // Increase of 3 for each level
  @if $type == "top" {
    $adjustment: -15;
  } @else if $type == "bottom" {
    $adjustment: -20;
  } @else {
    @return $base-color; // Default to base color if type is invalid
  }

  $r: red($base-color) + $adjustment + $amount;
  $g: green($base-color) + $adjustment + $amount;
  $b: blue($base-color) + $adjustment + $amount;

  @return rgb(min($r, 255), min($g, 255), min($b, 255));
}

@import "/node_modules/tabulator-tables/src/scss/tabulator.scss";

.tabulator{

	border: none !important;

	.tabulator-header{
		border-bottom: 3px solid $headerSeparatorColor;
		margin-bottom: 0px;
		padding-left: $handleWidth;
		background-color: $headerBackgroundColor;
		font-size: 1.0em;
		font-weight: 500;

		.tabulator-col{
			border-right:2px solid $headerBorderColor;
			background-color: $headerBackgroundColor;

			&:nth-child(1) {
				padding-left: $handleWidth;
			}

			.tabulator-col-content{
				padding: 2px;

				.tabulator-col-title{
					.tabulator-title-editor{
						border:1px solid $primary;

						font-size: 1em;
						color: $primary;
					}
				}
			}

			&.whiteBorder {
				padding-left: 10px;
			}

			&.whiteBorder::before {
				content: '';
				position: absolute;
				left: 0;
				top: 15%;
				bottom: 15%;
				width: 2px;
				background-color: #61a6d5;
			}

			&.tabulator-col-group{
				.tabulator-col-group-cols{
					border-top:2px solid $headerSeparatorColor;
				}
			}
		}

		.tabulator-frozen{
			&.tabulator-frozen-left{
				padding-left: $handleWidth;
			}
		}

		.tabulator-calcs-holder{
			border-top:2px solid $headerSeparatorColor !important;

			.tabulator-row{
				padding-left: 0 !important;

				.tabulator-cell{
					background:none;
				}
			}
		}
	}

	.tabulator-tableholder{
		border-left: 2px solid #cccccc;
		border-right: 2px solid #cccccc;
		border-bottom: 2px solid #e4e4e4;
		.tabulator-placeholder{
			span{
				color: $primary;
			}
		}

		.tabulator-table{
			.tabulator-row{

				&.tabulator-calcs{
					&.tabulator-calcs-top{
						background-color: rgba(255, 0, 0, 0) !important;
						border: none !important;
					}

					&.tabulator-calcs-bottom{
						border: none !important;
					}
				}
			}
		}
	}

	.tabulator-footer{
		.tabulator-calcs-holder{
			border-top:3px solid $headerSeparatorColor !important;
			border-bottom:2px solid $headerSeparatorColor !important;

			.tabulator-row{
				background:lighten($footerBackgroundColor, 5%) !important;

				.tabulator-cell{
					background:none;

					&:first-child{
						border-left: $handleWidth solid transparent;
					}
				}
			}

			&:only-child{
				border-bottom:none !important;
			}
		}

		.tabulator-spreadsheet-tabs{
			.tabulator-spreadsheet-tab{
				border-color:$footerBorderColor;

				color:$rowTextColor;
				font-weight: normal;

				&.tabulator-spreadsheet-tab-active{
					font-weight: bold;
					color:$footerTextColor;
				}
			}
		}
				//pagination button
		.tabulator-page{
			display:inline-block;
			
			margin:0 2px;
			padding:2px 5px;
			
			border:1px solid $footerBorderColor;
			border-radius:3px;
			
			background:rgba(255,255,255,.2);
			
			&.active{
				color: $backgroundColor;
				background-color: $darkBlueTextColor;
			}
			
			&:disabled{
				opacity:.5;
			}
			
			&:not(disabled){
				@media (hover:hover) and (pointer:fine){
					&:hover{
						cursor:pointer;
						background: $lightBlueTextColor;
						color:#fff;
					}
				}
			}
		}
	}
}


.tabulator-row{
	padding-top: 6px;
	border-top: 1px #d3d3d3 solid;
	border-bottom: 1px #e4e4e4 solid;

	.tabulator-cell{
		// &:first-child{
		// 	border-left: $handleWidth solid $handleColor;
		// }

		&.tabulator-row-header{
			//background-color: $handleColor;
			color:#fff;
		}
		&.lightBlue{
			color: $lightBlueTextColor !important;
		}
		&.darkBlue{
			color: $darkBlueTextColor !important;
		}
	}


	&:nth-child(even){
		//background-color: $handleColorAlt;

		.tabulator-cell{
			//background-color: $rowAltBackgroundColor;

			// &:first-child{
			// 	border-left: $handleWidth solid $handleColorAlt;
			// }

			&.tabulator-row-header{
				background-color: $handleColorAlt;
			}
		}
	}

	@media (hover:hover) and (pointer:fine){
		&.tabulator-selectable:hover{
			cursor: pointer;

			.tabulator-cell{
				background-color:$rowHoverBackground;
			}
		}
	}

	&.tabulator-selected{
		.tabulator-cell{
			background-color:$rowSelectedBackground;
		}
	}

	@media (hover:hover) and (pointer:fine){
		&.tabulator-selected:hover{
			.tabulator-cell{
				background-color:$rowSelectedBackgroundHover;
				cursor: pointer;
			}
		}
	}

	&.tabulator-moving{
		pointer-events: none !important;
	}

	.tabulator-cell{
		padding: 0px 1px;
		border: none !important;
		color: $primary !important;
		background-color: $rowBackgroundColor;
	}

	&.tabulator-group{
		min-width: 100%;

		//padding-bottom: 0px;

		border-right:none;

		//background:lighten($primary, 20%);

		.tabulator-arrow{
			border-left: 6px solid $groupArrowColor;
		}
		span{
			color:$primary;
		}
		.tabulator-cell{
			background-color: rgba(255, 0, 0, 0) !important;
		}
		&.tabulator-group-visible{
			.tabulator-arrow{
				border-top: 6px solid $groupArrowColor;
			}
		}
	}
}

.tabulator-toggle{
	&.tabulator-toggle-on{
		background:$primary;
	}
}

.tabulator-edit-select-list{
	border:1px solid $editBoxColor;
}

.tabulator-print-table{

	.tabulator-print-table-group{
		border-bottom:2px solid $primary;
		border-top:2px solid $primary;
		background:lighten($primary, 20%);
		margin-bottom: 2px;

		span{
			color:$primary;
		}
	}
}

.tabulator-popup {
	border-radius: 5px;
	font-family: MuseoSans-300;
	color: #333333;
	font-weight: bold;
}

.tabulator-popup-container {
	background-color: $popupBackgroundColor !important;

	.filterButtonContainer {
		display: flex;
		justify-content: space-between;
		border-top: 1px solid #e4e4e4;
		margin-top: 10px;
		margin-bottom: 5px;

		.filterButton {
			border: 0px;
			width: 100%;
			padding: 5px;
			text-align: left;
			cursor: pointer;
			margin-top: 10px;
			background-color: #f0f0f0;
			color: #333333;
			cursor: pointer;
		}

		button {
			border: 0px;
			width: 100%;
			padding: 5px;
			text-align: left;
			cursor: pointer;
			margin-top: 10px;
		}

		.filterClearButton{
			background-color: #f0f0f0;
			color: #333333;
			flex: 1;
			margin-right: 5px;
			border-radius: 3px;
		}

		.filterApplyButton{
			background-color: #0071bc;
			color: #ffffff;
			font-weight: bold;
			border: none;
			border-radius: 3px;
			flex: 1;
			margin-left: 5px;
		}
	
	}

	.filterIcon {
		color: #7fb0d9;
		font-size: 14px;
		float: left;
		padding-top: 4px;
	}

	.filterLabel {
		display: block;
		margin-bottom: 5px;
		font-size: 1.1em;
		font-style: italic;
		font-weight: normal;
		color: #b7b7b7;
	}

	.filterInput {
		width: 100%;
		border-top: 1px solid #e8e8e8;
		border-left: 1px solid #e3e3e3;
		border-right: 1px solid #ebebeb;
		border-bottom: 1px solid #e3e3e3;
		border-radius: 3px;
		padding: 5px;
		box-sizing: border-box;
		margin-bottom: 10px;
		font-style: italic;
		color: #a3a3a3;

		&:focus {
			outline: none;
			border-color: #99d2f8;
			box-shadow: 0 0 3px rgba(153, 210, 248, 0.5);
		}
	}
}

// Apply the variables and functions to group levels
.tabulator-group-level-0 {
	padding-left: 10px !important;
	background-color: lighten-background($base-background-color, 0) !important;
	color: $primary;
	border-top: adjust-border-color($base-background-color, 0, "top") solid 1px !important;
	border-bottom: adjust-border-color($base-background-color, 0, "bottom") solid 1px !important;
}

.tabulator-group-level-1 {
	padding-left: 110px !important;
	background-color: lighten-background($base-background-color, 1) !important;
	color: $primary;
	border-top: adjust-border-color($base-background-color, 1, "top") solid 1px !important;
	border-bottom: adjust-border-color($base-background-color, 1, "bottom") solid 1px !important;
}

.tabulator-group-level-2 {
	padding-left: 210px !important;
	background-color: lighten-background($base-background-color, 2) !important;
	color: $primary;
	border-top: adjust-border-color($base-background-color, 2, "top") solid 1px !important;
	border-bottom: adjust-border-color($base-background-color, 2, "bottom") solid 1px !important;
}

.tabulator-group-level-3 {
	padding-left: 310px !important;
	background-color: lighten-background($base-background-color, 3) !important;
	color: $primary;
	border-top: adjust-border-color($base-background-color, 3, "top") solid 1px !important;
	border-bottom: adjust-border-color($base-background-color, 3, "bottom") solid 1px !important;
}

.tabulator-group-level-4 {
	padding-left: 130px;
	background-color: lighten-background($base-background-color, 4) !important;
	color: $primary;
	border-top: adjust-border-color($base-background-color, 4, "top") solid 1px !important;
	border-bottom: adjust-border-color($base-background-color, 4, "bottom") solid 1px !important;
}

.tabulator-col-sorter {
	color: #66a9d7 !important;
}

.tabulator-col[aria-sort="descending"] .tabulator-col-sorter i{
	color:#66a9d7;
	transform: scaleY(-1); // flip the icon vertically so the arrow points down
}

.svg-icon {
	display: inline-block;
	width: 20px;
	height: 20px;
	vertical-align: top;

	&.satisfied-icon::before {
	  content: '';
	  display: inline-block;
	  background: url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" height="20" width="20"%3E%3Ccircle r="5" cx="10" cy="10" stroke="%23c0cad0" stroke-width="2" fill="%2399d2f8" /%3E%3C/svg%3E') no-repeat center;
	  width: 100%;
	  height: 100%;
	  background-size: contain;
	}

	&.unsatisfied-icon::before {
	  content: '';
	  display: inline-block;
	  background: url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" height="20" width="20"%3E%3Ccircle r="5" cx="10" cy="10" stroke="%23c0cad0" stroke-width="2" fill="%230089e3" /%3E%3C/svg%3E') no-repeat center;
	  width: 100%;
	  height: 100%;
	  background-size: contain;
	}
}

.clickable-cell {
  cursor: pointer !important;
  transition: background-color 0.2s ease;
}

.clickable-cell:hover {
  background-color: #f0f8ff !important;
  color: #0071BC !important;
}

//remove the bullet points from the list on the report details table
.no-list-style ul {
  padding: 0;
  margin: 0;
  list-style-type: none !important;
}