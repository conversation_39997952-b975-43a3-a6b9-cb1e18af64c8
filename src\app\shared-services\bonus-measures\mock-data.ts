import { BonusMeasure } from './model/bonus-measures-model';

// Mock data based on your API response structure
export const mockBonusMeasures: BonusMeasure[] = [
  {
    rowNum: 1,
    reportId: 23,
    measureId: 130,
    measureCd: "AHF016",
    cohortName: null,
    location: "AA_NEWNAN HCC",
    primaryProvider: "AHMED MOUSSA, MD",
    reportingPeriod: 2025,
    rollingWeek: 27,
    invertedFlg: false,
    cohortDescription: null,
    totCohortCnt: 776,
    totSchedCnt: null,
    totVisitCnt: null,
    measureNm: "AHF016-HIV Viral Load Suppression (Rolling Year)",
    measureOrd: "AHF016",
    cohortCnt: 688,
    reportLevelDesc1: "Individual",
    reportLevelDesc2: "Effective Clinical Care",
    reportLevelDesc3: null,
    meetingDesc: "Percentage of patients, regardless of age, with a diagnosis of HIV, at least 1 visit in the past 12 months, with a HIV viral load less than 200 copies/mL at last HIV viral load test during the past 12 months.",
    notMeetingDesc: "Percentage of patients, regardless of age, with a diagnosis of HIV, at least 1 visit in the past 12 months, with a HIV viral load less than 200 copies/mL at last HIV viral load test during the past 12 months.",
    cohortId: 1004,
    displayOrderNo: 1,
    locationGroupId: 3,
    locationGroupName: "Southern - North",
    satisfiedPercentage: 89,
    unsatisfiedPercentage: 11,
    totalMeasures: 776,
    unsatisfiedCount: 88
  },
  {
    rowNum: 2,
    reportId: 23,
    measureId: 131,
    measureCd: "AHF017",
    cohortName: null,
    location: "AA_NEWNAN HCC",
    primaryProvider: "SARAH JOHNSON, MD",
    reportingPeriod: 2025,
    rollingWeek: 27,
    invertedFlg: false,
    cohortDescription: null,
    totCohortCnt: 542,
    totSchedCnt: null,
    totVisitCnt: null,
    measureNm: "AHF016-HIV Viral Load Suppression (Rolling Year)",
    measureOrd: "AHF017",
    cohortCnt: 456,
    reportLevelDesc1: "Individual",
    reportLevelDesc2: "Effective Clinical Care",
    reportLevelDesc3: null,
    meetingDesc: "Percentage of patients, regardless of age, with a diagnosis of HIV, at least 1 visit in the past 12 months, with a HIV viral load less than 200 copies/mL at last HIV viral load test during the past 12 months.",
    notMeetingDesc: "Percentage of patients, regardless of age, with a diagnosis of HIV, at least 1 visit in the past 12 months, with a HIV viral load less than 200 copies/mL at last HIV viral load test during the past 12 months.",
    cohortId: 1005,
    displayOrderNo: 2,
    locationGroupId: 3,
    locationGroupName: "Southern - North",
    satisfiedPercentage: 84,
    unsatisfiedPercentage: 16,
    totalMeasures: 542,
    unsatisfiedCount: 86
  },
  {
    rowNum: 3,
    reportId: 23,
    measureId: 132,
    measureCd: "AHF018",
    cohortName: null,
    location: "BB_ATLANTA CENTRAL",
    primaryProvider: "MICHAEL DAVIS, MD",
    reportingPeriod: 2025,
    rollingWeek: 27,
    invertedFlg: false,
    cohortDescription: null,
    totCohortCnt: 923,
    totSchedCnt: null,
    totVisitCnt: null,
    measureNm: "AHF018-HIV Care Retention (Rolling Year)",
    measureOrd: "AHF018",
    cohortCnt: 834,
    reportLevelDesc1: "Individual",
    reportLevelDesc2: "Effective Clinical Care",
    reportLevelDesc3: null,
    meetingDesc: "Percentage of patients with HIV who had at least two visits for HIV medical care in the past 12 months, with visits at least 90 days apart.",
    notMeetingDesc: "Percentage of patients with HIV who had at least two visits for HIV medical care in the past 12 months, with visits at least 90 days apart.",
    cohortId: 1006,
    displayOrderNo: 3,
    locationGroupId: 4,
    locationGroupName: "Central - Metro",
    satisfiedPercentage: 90,
    unsatisfiedPercentage: 10,
    totalMeasures: 923,
    unsatisfiedCount: 89
  },
  {
    rowNum: 4,
    reportId: 23,
    measureId: 133,
    measureCd: "AHF019",
    cohortName: null,
    location: "BB_ATLANTA CENTRAL",
    primaryProvider: "JENNIFER WILSON, MD",
    reportingPeriod: 2025,
    rollingWeek: 27,
    invertedFlg: false,
    cohortDescription: null,
    totCohortCnt: 678,
    totSchedCnt: null,
    totVisitCnt: null,
    measureNm: "AHF018-HIV Care Retention (Rolling Year)",
    measureOrd: "AHF019",
    cohortCnt: 589,
    reportLevelDesc1: "Individual",
    reportLevelDesc2: "Effective Clinical Care",
    reportLevelDesc3: null,
    meetingDesc: "Percentage of patients with HIV who had at least two visits for HIV medical care in the past 12 months, with visits at least 90 days apart.",
    notMeetingDesc: "Percentage of patients with HIV who had at least two visits for HIV medical care in the past 12 months, with visits at least 90 days apart.",
    cohortId: 1007,
    displayOrderNo: 4,
    locationGroupId: 4,
    locationGroupName: "Central - Metro",
    satisfiedPercentage: 87,
    unsatisfiedPercentage: 13,
    totalMeasures: 678,
    unsatisfiedCount: 89
  },
  {
    rowNum: 5,
    reportId: 23,
    measureId: 134,
    measureCd: "AHF020",
    cohortName: null,
    location: "CC_NORTH CLINIC",
    primaryProvider: "ROBERT BROWN, MD",
    reportingPeriod: 2025,
    rollingWeek: 27,
    invertedFlg: false,
    cohortDescription: null,
    totCohortCnt: 445,
    totSchedCnt: null,
    totVisitCnt: null,
    measureNm: "AHF020-HIV Medication Adherence (Rolling Year)",
    measureOrd: "AHF020",
    cohortCnt: 378,
    reportLevelDesc1: "Individual",
    reportLevelDesc2: "Effective Clinical Care",
    reportLevelDesc3: null,
    meetingDesc: "Percentage of patients with HIV who demonstrate medication adherence of 80% or greater.",
    notMeetingDesc: "Percentage of patients with HIV who demonstrate medication adherence of 80% or greater.",
    cohortId: 1008,
    displayOrderNo: 5,
    locationGroupId: 5,
    locationGroupName: "Northern - Region",
    satisfiedPercentage: 85,
    unsatisfiedPercentage: 15,
    totalMeasures: 445,
    unsatisfiedCount: 67
  }
];
