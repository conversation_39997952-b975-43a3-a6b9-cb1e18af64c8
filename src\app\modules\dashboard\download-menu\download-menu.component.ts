import { Component, EventEmitter, Output } from '@angular/core';

@Component({
  selector: 'app-download-menu',
  templateUrl: './download-menu.component.html',
  styleUrls: ['./download-menu.component.css']
})
export class DownloadMenuComponent {
  // Event emitters for download actions
  @Output() printClicked = new EventEmitter<void>();
  @Output() pdfExportClicked = new EventEmitter<void>();
  @Output() excelExportClicked = new EventEmitter<void>();
  @Output() csvExportClicked = new EventEmitter<void>();

  constructor() { }

  // Event handler methods
  onPrintClick(): void {
    this.printClicked.emit();
  }

  onPdfExportClick(): void {
    this.pdfExportClicked.emit();
  }

  onExcelExportClick(): void {
    this.excelExportClicked.emit();
  }

  onCsvExportClick(): void {
    this.csvExportClicked.emit();
  }
}
