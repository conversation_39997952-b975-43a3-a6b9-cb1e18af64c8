export interface BonusMeasure {
  rowNum: number;
  reportId: number;
  measureId: number;
  measureCd: string;
  cohortName: string | null;
  location: string;
  primaryProvider: string;
  reportingPeriod: number;
  rollingWeek: number;
  invertedFlg: boolean;
  cohortDescription: string | null;
  totCohortCnt: number;
  totSchedCnt: number | null;
  totVisitCnt: number | null;
  measureNm: string;
  measureOrd: string;
  cohortCnt: number;
  reportLevelDesc1: string;
  reportLevelDesc2: string;
  reportLevelDesc3: string | null;
  meetingDesc: string;
  notMeetingDesc: string;
  cohortId: number;
  displayOrderNo: number;
  locationGroupId: number;
  locationGroupName: string;
  
  // Calculated fields for display
  satisfiedPercentage?: number;
  unsatisfiedPercentage?: number;
  totalMeasures?: number;
  unsatisfiedCount?: number;
}
