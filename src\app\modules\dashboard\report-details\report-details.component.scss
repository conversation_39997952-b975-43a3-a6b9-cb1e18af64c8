/* Report Details Component Styles */
.report-details-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  font-family: MuseoSans-300;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Header */
.header {
  padding: 16px 24px;
  border-bottom: 1px solid #e0e0e0;
  background-color: #f9f9f9;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.patient-count {
  font-size: 1.1rem;
  font-weight: 500;
  color: #333;
}

/* Export Dropdown Styles */
.export-dropdown {
  position: relative;
}

/* Loading State */
.loading-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.loading-content {
  text-align: center;
}

/* No Data State */
.no-data-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.no-data-content {
  text-align: center;
  color: #6c757d;
}

.no-data-icon {
  font-size: 48px;
  color: #dee2e6;
  margin-bottom: 16px;
}

.no-data-content h3 {
  margin: 16px 0 8px 0;
  color: #495057;
}

.no-data-content p {
  margin: 0;
  font-size: 14px;
}

/* Table Container */
.table-container {
  flex: 1;
  overflow: hidden;
  border-radius: 12px;
  background-color: #f9f9f9;
}

#report-details-table {
  height: 100%;
}
