<!-- Bonus Measures Dashboard -->
<div class="report-wrapper">
  <!-- Report Header -->
  <div class="report-header">
    <div class="header-left">
      <h1 class="report-title">Bonus Measures</h1>
    </div>
  </div>

  <!-- Report Content -->
  <div class="report-content-layout">
    <!-- Main Content Area -->
    <div class="report-viewer-area">

      <!-- Main Bonus Measures Table -->
      <div class="bonus-measures-container" [style.display]="showReportDetails ? 'none' : 'block'">
        <div class="header">
          <div class="header-content">
            <span class="patient-count">{{ getTotalMeasuresSum() | number }} Patients</span>
            <div class="export-dropdown">
              <app-download-menu
                (printClicked)="printTable()"
                (pdfExportClicked)="downloadPDF()"
                (excelExportClicked)="downloadExcel()"
                (csvExportClicked)="downloadCSV()">
              </app-download-menu>
            </div>
          </div>
        </div>

        <!-- Tabulator Table Container -->
        <div class="table-container">
          <div id="bonus-measures-table"></div>
        </div>
      </div>

      <!-- Report Details Component -->
      <div class="report-details-container" [style.display]="showReportDetails ? 'block' : 'none'">
        <div class="details-header">
          <button class="back-button" (click)="closeReportDetails()">
            <mat-icon class="material-symbols-outlined">arrow_back</mat-icon>
            Back to Bonus Measures
          </button>
        </div>

        <app-report-details
          [siteId]="selectedRowData?.siteId"
          [year]="selectedRowData?.year"
          [cohortId]="selectedRowData?.cohortId"
          [locationCd]="selectedRowData?.locationCd"
          [providerCd]="selectedRowData?.providerCd"
          [rollingWeek]="selectedRowData?.rollingWeek"
          [alertLvl]="selectedRowData?.alertLvl"
          [measuresCd]="selectedRowData?.measuresCd">
        </app-report-details>
      </div>

    </div>
  </div>
</div>
